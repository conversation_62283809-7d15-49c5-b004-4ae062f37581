
import asyncio
from claude_code_sdk import query, ClaudeCodeO<PERSON><PERSON>, create_sdk_mcp_server, tool, ClaudeSDKClient
import json
from pypdf import <PERSON>df<PERSON>eader, PdfWriter


@tool("get_form_fields", "Extract form fields from a PDF", {"path": str})
async def get_form_fields(args):
    reader = PdfReader(args["path"])
    fields = {}
    
    if "/AcroForm" in reader.trailer["/Root"]:
        form = reader.trailer["/Root"]["/AcroForm"]
        form_fields = form.get("/Fields", [])
        for field in form_fields:
            field_obj = field.get_object()
            name = field_obj.get("/T")
            if not name:
                continue

            # Get the field type
            field_type = field_obj.get("/FT")  # /Tx (text), /Btn (button/checkbox), /Ch (choice)
            if field_type == "/Tx":
                type_str = "text"
            elif field_type == "/Btn":
                type_str = "button"
            elif field_type == "/Ch":
                type_str = "choice"
            else:
                type_str = "unknown"

            # Get the current value
            value = field_obj.get("/V", "")

            fields[name] = {
                "type": type_str,
                "value": value
            }

    return {
        "content": [{
            "type": "text",
            "text": json.dumps(fields, indent=2)
        }]
    }

@tool("fill_form_fields", "Fill a PDF form with given field values", {"path": str, "field_values": dict, "output_path": str})
async def fill_form_fields(args):
    """
    Args:
        path: Path to the input PDF with AcroForm fields.
        field_values: Dict mapping field names to values to fill.
        output_path: Path to save the filled PDF.
    """
    input_path = args["path"]
    field_values = args["field_values"]
    output_path = args["output_path"]

    reader = PdfReader(input_path)
    writer = PdfWriter()

    # Copy pages and update form fields
    for page in reader.pages:
        writer.add_page(page)

    if "/AcroForm" in reader.trailer["/Root"]:
        form = reader.trailer["/Root"]["/AcroForm"]
        form_fields = form.get("/Fields", [])
        for field in form_fields:
            field_obj = field.get_object()
            name = field_obj.get("/T")
            if name and name in field_values:
                field_obj.update({
                    "/V": field_values[name],
                    "/Ff": field_obj.get("/Ff", 0)
                })

        # Preserve the AcroForm dictionary
        writer._root_object.update({"/AcroForm": form})

    # Write filled PDF
    with open(output_path, "wb") as f_out:
        writer.write(f_out)

    return {
        "content": [{
            "type": "text",
            "text": f"Form filled and saved to {output_path}"
        }]
    }



forms = create_sdk_mcp_server(
    name="forms",
    version="1.0.0",
    tools=[get_form_fields, fill_form_fields]  # Pass decorated functions
)

# Use with Claude
options = ClaudeCodeOptions(
    mcp_servers={"forms": forms},
    allowed_tools=["Read", "Write", "Edit","mcp__forms__get_form_fields", "mcp__forms__fill_form_fields"]
)




input_pdf = "sample_form.pdf"         # Replace with your test PDF
filled_pdf = "filled_form.pdf"

# --- Step 1: Extract fields ---
print("Extracting form fields...")
fields_response = await get_form_fields({"path": input_pdf})
fields_json = fields_response["content"][0]["text"]
fields = json.loads(fields_json)
print("Fields found:", fields)

# --- Step 2: Prepare values to fill ---
field_values = {}
for field_name in fields:
    field_values[field_name] = f"Test_{field_name}"  # Fill each field with sample text

# --- Step 3: Fill form ---
print("Filling form...")
fill_response = await fill_form({
    "path": input_pdf,
    "field_values": field_values,
    "output_path": filled_pdf
})
print(fill_response["content"][0]["text"])





form_path = "forms/fw9.pdf"

prompt = f"""
Extract the form fields from the following PDF:

{form_path}


"""

async with ClaudeSDKClient(options=options) as client:
    await client.query(prompt)

    # Extract and print response
    async for msg in client.receive_response():
        print(msg)



async def get_form_fields(args):
    reader = PdfReader(args["path"])
    fields = {}
    
    if "/AcroForm" in reader.trailer["/Root"]:
        form = reader.trailer["/Root"]["/AcroForm"]
        form_fields = form.get("/Fields", [])
        for field in form_fields:
            field_obj = field.get_object()
            name = field_obj.get("/T")
            if not name:
                continue

            # Get the field type
            field_type = field_obj.get("/FT")  # /Tx (text), /Btn (button/checkbox), /Ch (choice)
            if field_type == "/Tx":
                type_str = "text"
            elif field_type == "/Btn":
                type_str = "button"
            elif field_type == "/Ch":
                type_str = "choice"
            else:
                type_str = "unknown"

            # Get the current value
            value = field_obj.get("/V", "")

            fields[name] = {
                "type": type_str,
                "value": value
            }

    return {
        "content": [{
            "type": "text",
            "text": json.dumps(fields, indent=2)
        }]
    }



await get_form_fields({"path": "forms/job-app-form-EDITED.pdf"})

fields = {
  "FULL NAME": {"type": "text", "value": "Jane Doe"},
  "DATE": {"type": "text", "value": "2025-09-27"},
  "ADDRESS": {"type": "text", "value": "123 Main St"},
  "City": {"type": "text", "value": "Springfield"},
  "State": {"type": "text", "value": "IL"},
  "Zip Code": {"type": "text", "value": "62704"},
  "YES LEGALLY ELIGIBLE TO WORK IN US": {"type": "button", "value": "X"},
  "NO LEGALLY ELIGIBLE TO WORK IN US": {"type": "button", "value": ""}
}

def fill_form_fields(args):
    """
    Args:
        path: Path to the input PDF with AcroForm fields.
        field_values: Dict mapping field names to values to fill.
        output_path: Path to save the filled PDF.
    """
    input_path = args["path"]
    field_values = args["field_values"]
    output_path = args["output_path"]

    reader = PdfReader(input_path)
    writer = PdfWriter()

    # Copy pages and update form fields
    for page in reader.pages:
        writer.add_page(page)

    if "/AcroForm" in reader.trailer["/Root"]:
        form = reader.trailer["/Root"]["/AcroForm"]
        form_fields = form.get("/Fields", [])
        for field in form_fields:
            field_obj = field.get_object()
            name = field_obj.get("/T")
            if name and name in field_values:
                field_obj.update({
                    "/V": field_values[name],
                    "/Ff": field_obj.get("/Ff", 0)
                })

        # Preserve the AcroForm dictionary
        writer._root_object.update({"/AcroForm": form})

    # Write filled PDF
    with open(output_path, "wb") as f_out:
        writer.write(f_out)

    return {
        "content": [{
            "type": "text",
            "text": f"Form filled and saved to {output_path}"
        }]
    }

fill_form_fields({"path": "forms/job-app-form-EDITED.pdf", "field_values": fields, "output_path": "filled.pdf"})

