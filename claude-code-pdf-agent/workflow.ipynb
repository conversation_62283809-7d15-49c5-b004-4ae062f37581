!pip3 install claude-code-sdk

import claude_code_sdk
claude_code_sdk.__version__

import anyio
from claude_code_sdk import query

async def main():
    async for message in query(prompt="What is 2 + 2?"):
        print(message)

await main()

from claude_code_sdk import tool, create_sdk_mcp_server, ClaudeCodeOptions, ClaudeSDKClient

options = ClaudeCodeOptions(
    mcp_servers={
        "pdf-filler": {
            "command": "node",
            "args": ["/Users/<USER>/Downloads/pdf-filler-simple/server/index.js"]
        }
    },
    allowed_tools=['Task', 'Bash', 'Glob', 'Grep', 'ExitPlanMode', 'Read', 'Edit', 'MultiEdit', 'Write', 'NotebookEdit', 'WebFetch', 'TodoWrite', 'WebSearch', 'BashOutput', 'KillShell', 'SlashCommand', 'mcp__pdf-filler__list_pdfs', 'mcp__pdf-filler__read_pdf_fields', 'mcp__pdf-filler__fill_pdf', 'mcp__pdf-filler__bulk_fill_from_csv', 'mcp__pdf-filler__save_profile', 'mcp__pdf-filler__load_profile', 'mcp__pdf-filler__list_profiles', 'mcp__pdf-filler__fill_with_profile', 'mcp__pdf-filler__extract_to_csv', 'mcp__pdf-filler__validate_pdf', 'mcp__pdf-filler__read_pdf_content', 'mcp__pdf-filler__get_pdf_resource_uri']
)




input_dir_location = "input"
template_file_location = "forms/job-app-form.pdf"
output_file_location = "output/filled-form.pdf"

prompt = f"Use the information in the PDFs in the {input_dir_location}/ dir to fill out {template_file_location} and save as {output_file_location}."

async with ClaudeSDKClient(options=options) as client:
    await client.query(prompt)

    # Extract and print response
    async for msg in client.receive_response():
        print(msg)

input_dir_location = "input"
template_file_location = "forms/sample_form.pdf"
output_file_location = "output/filled-form.pdf"

prompt = f"""Use the information in the PDFs in the {input_dir_location}/ dir to fill out {template_file_location} and save as {output_file_location}. 

Validate that the PDF was filled out correctly."""

async with ClaudeSDKClient(options=options) as client:
    await client.query(prompt)

    # Extract and print response
    async for msg in client.receive_response():
        print(msg)

input_dir_location = "input"

prompt = f"""what are all the fields present in forms/fw9.pdf, and can you generate a CSV of all fields and their descriptions in natural language? 

You may have to look at more than just the extracted fields and look at the PDF as an image or whole form.

Use the CSV and the mapped fields to fill out forms/fw9.pdf with the information in the PDFs in the {input_dir_location}/ dir.

Save the filled out form as output/fw9-filled.pdf. Verify that the fields are filled out correctly, with both extracted text from the PDF and visual inspection.
"""

async with ClaudeSDKClient(options=options) as client:
    await client.query(prompt)

    # Extract and print response
    async for msg in client.receive_response():
        print(msg)