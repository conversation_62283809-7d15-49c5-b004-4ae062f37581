

!pip install pypdf

import json
from pypdf import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter

PDF_PATH = "forms/job-app-form-EDITED.pdf"          # Input form (must be an AcroForm PDF)
OUTPUT_PDF_PATH = "filled.pdf" # Output after filling
MAPPING_JSON = "field_mapping.json"  # Store normalized names here

reader = PdfReader(PDF_PATH)
fields = reader.get_fields()
fields





def get_form_fields(pdf_path):
    reader = PdfReader(pdf_path)
    fields = {}
    if "/AcroForm" in reader.trailer["/Root"]:
        form = reader.trailer["/Root"]["/AcroForm"]
        form_fields = form.get("/Fields", [])
        for field in form_fields:
            field_obj = field.get_object()
            print(field_obj)
            name = field_obj.get("/T")
            if name:
                fields[name] = ""
    return fields

fields = get_form_fields(PDF_PATH)
print("Extracted Fields:", fields)

# Step 3: Create or update mapping (normalize junk field names → human-readable)

# If no mapping exists, create a stub mapping
try:
    with open(MAPPING_JSON, "r") as f:
        field_mapping = json.load(f)
except FileNotFoundError:
    field_mapping = {k: f"Normalized_{i}" for i, k in enumerate(fields.keys())}
    with open(MAPPING_JSON, "w") as f:
        json.dump(field_mapping, f, indent=2)

print("Field Mapping (raw→normalized):")
print(json.dumps(field_mapping, indent=2))


# Step 4: Example data to fill (based on normalized names)
# Imagine this comes from an LLM output
normalized_data = {
    "FULL NAME": "Alice",
    "ADDRESS": "Johnson",
    "Has Insurance": "Yes"
}

# Invert mapping: normalized → raw
inv_mapping = {v: k for k, v in field_mapping.items()}

# Step 5: Fill form fields
def fill_pdf(input_pdf, output_pdf, inv_mapping, normalized_data):
    reader = PdfReader(input_pdf)
    writer = PdfWriter()

    for page in reader.pages:
        writer.add_page(page)

    # Prepare field values using raw names
    field_values = {}
    for norm_name, value in normalized_data.items():
        raw_name = inv_mapping.get(norm_name)
        if raw_name:
            field_values[raw_name] = value

    writer.update_page_form_field_values(writer.pages[0], field_values)

    with open(output_pdf, "wb") as f:
        writer.write(f)

fill_pdf(PDF_PATH, OUTPUT_PDF_PATH, inv_mapping, normalized_data)
print(f"✅ Filled PDF saved to {OUTPUT_PDF_PATH}")


from pypdf import PdfReader, PdfWriter

reader = PdfReader("forms/job-app-form-EDITED.pdf")
writer = PdfWriter()

page = reader.pages[0]
fields = reader.get_fields()

writer.append(reader)

# writer.update_page_form_field_values(
#     writer.pages[0],
#     {"fieldname": "some filled in text"},
#     auto_regenerate=False,
# )

# with open("filled-out.pdf", "wb") as output_stream:
#     writer.write(output_stream)

fields

writer.update_page_form_field_values(
    writer.pages[0],
    {"FULL NAME": "some filled in text"},
    auto_regenerate=False,
)

with open("filled-out.pdf", "wb") as output_stream:
    writer.write(output_stream)

